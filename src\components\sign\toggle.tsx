"use client";

import User from "./user";
import { useAppContext } from "@/contexts/app";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";

export default function SignToggle() {
  const t = useTranslations();
  const { user } = useAppContext();

  const handleSignIn = () => {
    // 重定向到NextAuth默认登录页面
    const callbackUrl = encodeURIComponent(window.location.href);
    window.location.href = `/api/auth/signin?callbackUrl=${callbackUrl}`;
  };

  return (
    <div className="flex items-center">
      {user ? (
        <User user={user} />
      ) : (
        <Button
          variant="default"
          onClick={handleSignIn}
          className="cursor-pointer"
        >
          {t("user.sign_in")}
        </Button>
      )}
    </div>
  );
}
