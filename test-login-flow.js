#!/usr/bin/env node

/**
 * 测试登录流程和拦截功能
 */

const https = require('https');
const http = require('http');

const SITE_URL = 'http://localhost:3000';

console.log('🧪 测试登录流程和拦截功能...');
console.log(`测试网站: ${SITE_URL}`);

// 1. 测试NextAuth默认登录页面
function testNextAuthSignInPage() {
  return new Promise((resolve) => {
    console.log('\n📄 测试NextAuth默认登录页面:');
    
    const url = `${SITE_URL}/api/auth/signin`;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      console.log(`📊 状态码: ${res.statusCode}`);
      
      if (res.statusCode === 200) {
        let html = '';
        res.on('data', chunk => html += chunk);
        res.on('end', () => {
          // 检查是否包含Google登录选项
          if (html.includes('Google') || html.includes('google')) {
            console.log('✅ NextAuth默认登录页面包含Google登录选项');
            resolve(true);
          } else {
            console.log('❌ NextAuth默认登录页面不包含Google登录选项');
            resolve(false);
          }
        });
      } else {
        console.log('❌ NextAuth默认登录页面无法访问');
        resolve(false);
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ 请求失败: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ 请求超时');
      resolve(false);
    });
  });
}

// 2. 测试主页是否正常加载
function testHomePage() {
  return new Promise((resolve) => {
    console.log('\n🏠 测试主页加载:');
    
    const url = SITE_URL;
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      console.log(`📊 状态码: ${res.statusCode}`);
      
      if (res.statusCode === 200) {
        let html = '';
        res.on('data', chunk => html += chunk);
        res.on('end', () => {
          // 检查是否包含上传按钮
          if (html.includes('upload') || html.includes('上传')) {
            console.log('✅ 主页包含上传功能');
            
            // 检查是否包含登录拦截逻辑
            if (html.includes('triggerFileInput') && html.includes('session')) {
              console.log('✅ 主页包含登录拦截逻辑');
              resolve(true);
            } else {
              console.log('❌ 主页缺少登录拦截逻辑');
              resolve(false);
            }
          } else {
            console.log('❌ 主页不包含上传功能');
            resolve(false);
          }
        });
      } else {
        console.log('❌ 主页无法访问');
        resolve(false);
      }
    });
    
    req.on('error', (err) => {
      console.log(`❌ 请求失败: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ 请求超时');
      resolve(false);
    });
  });
}

// 3. 测试NextAuth API端点
function testNextAuthAPI() {
  return new Promise((resolve) => {
    console.log('\n🔐 测试NextAuth API端点:');
    
    const endpoints = [
      '/api/auth/providers',
      '/api/auth/session',
      '/api/auth/csrf'
    ];
    
    let completedTests = 0;
    let passedTests = 0;
    
    endpoints.forEach(endpoint => {
      const url = `${SITE_URL}${endpoint}`;
      const protocol = url.startsWith('https') ? https : http;
      
      const req = protocol.get(url, (res) => {
        console.log(`📊 ${endpoint}: ${res.statusCode}`);
        
        if (res.statusCode === 200) {
          passedTests++;
          console.log(`✅ ${endpoint} 正常`);
        } else {
          console.log(`❌ ${endpoint} 异常`);
        }
        
        completedTests++;
        if (completedTests === endpoints.length) {
          const success = passedTests === endpoints.length;
          console.log(`📊 NextAuth API测试结果: ${passedTests}/${endpoints.length} 通过`);
          resolve(success);
        }
      });
      
      req.on('error', (err) => {
        console.log(`❌ ${endpoint} 请求失败: ${err.message}`);
        completedTests++;
        if (completedTests === endpoints.length) {
          const success = passedTests === endpoints.length;
          console.log(`📊 NextAuth API测试结果: ${passedTests}/${endpoints.length} 通过`);
          resolve(success);
        }
      });
      
      req.setTimeout(5000, () => {
        req.destroy();
        console.log(`❌ ${endpoint} 请求超时`);
        completedTests++;
        if (completedTests === endpoints.length) {
          const success = passedTests === endpoints.length;
          console.log(`📊 NextAuth API测试结果: ${passedTests}/${endpoints.length} 通过`);
          resolve(success);
        }
      });
    });
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试...\n');
  
  const results = {
    nextAuthSignIn: await testNextAuthSignInPage(),
    homePage: await testHomePage(),
    nextAuthAPI: await testNextAuthAPI()
  };
  
  console.log('\n📊 测试结果总结:');
  console.log(`NextAuth默认登录页面: ${results.nextAuthSignIn ? '✅ 通过' : '❌ 失败'}`);
  console.log(`主页登录拦截: ${results.homePage ? '✅ 通过' : '❌ 失败'}`);
  console.log(`NextAuth API端点: ${results.nextAuthAPI ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 所有测试通过！登录流程配置正确。');
    console.log('\n✅ 修复完成确认:');
    console.log('1. ✅ 移除了自定义登录页面，使用NextAuth默认页面');
    console.log('2. ✅ 登录拦截功能正常工作');
    console.log('3. ✅ NextAuth API端点正常运行');
    console.log('\n🔗 现在用户点击上传按钮后会看到标准的Google登录页面');
  } else {
    console.log('\n❌ 部分测试失败，请检查配置。');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// 运行测试
runTests().catch(console.error);
