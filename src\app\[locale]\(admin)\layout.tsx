import DashboardLayout from "@/components/dashboard/layout";
import AccessDenied from "@/components/admin/access-denied";
import { ReactNode } from "react";
import { Sidebar } from "@/types/blocks/sidebar";
import { getUserInfo } from "@/services/user";
import { isCurrentUserAdmin } from "@/services/admin";
import { redirect } from "next/navigation";

export default async function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  const userInfo = await getUserInfo();
  if (!userInfo || !userInfo.email) {
    redirect("/api/auth/signin");
  }

  // 检查管理员权限
  const isAdmin = await isCurrentUserAdmin();
  if (!isAdmin) {
    return <AccessDenied />;
  }

  const sidebar: Sidebar = {
    brand: {
      title: "水印去除器",
      logo: {
        src: "/logo.png",
        alt: "水印去除器",
      },
      url: "/admin",
    },
    nav: {
      items: [
        {
          title: "仪表板",
          url: "/admin",
          icon: "RiDashboardLine",
        },
      ],
    },
    library: {
      title: "菜单",
      items: [
        {
          title: "系统管理",
          url: "/admin/management",
          icon: "RiSettings3Line",
        },
        {
          title: "用户管理",
          url: "/admin/users",
          icon: "RiUserLine",
        },
        {
          title: "订单管理",
          icon: "RiOrderPlayLine",
          url: "/admin/orders",
        },
        {
          title: "文章管理",
          url: "/admin/posts",
          icon: "RiArticleLine",
        },
        {
          title: "反馈管理",
          url: "/admin/feedbacks",
          icon: "RiMessage2Line",
        },
        {
          title: "联系我们",
          url: "/admin/contacts",
          icon: "RiMailLine",
        },
        {
          title: "系统设置",
          url: "/admin/settings",
          icon: "RiSettings4Line",
        },
        {
          title: "系统日志",
          url: "/admin/logs",
          icon: "RiFileTextLine",
        },
      ],
    },
    bottomNav: {
      items: [
        {
          title: "Home",
          url: "/",
          target: "_self",
          icon: "RiHomeLine",
        },
        {
          title: "Pricing",
          url: "/pricing",
          target: "_self",
          icon: "RiPriceTag3Line",
        },
        {
          title: "Contact",
          url: "/contact",
          target: "_self",
          icon: "RiContactsLine",
        },
      ],
    },
    social: {
      items: [
        {
          title: "Home",
          url: "/",
          target: "_self",
          icon: "RiHomeLine",
        },
        {
          title: "Pricing",
          url: "/pricing",
          target: "_self",
          icon: "RiPriceTag3Line",
        },
        {
          title: "Contact",
          url: "/contact",
          target: "_self",
          icon: "RiContactsLine",
        },
      ],
    },
    account: {
      items: [
        {
          title: "Home",
          url: "/",
          icon: "RiHomeLine",
          target: "_blank",
        },
        {
          title: "Recharge",
          url: "/pricing",
          icon: "RiMoneyDollarBoxLine",
          target: "_blank",
        },
      ],
    },
  };

  return <DashboardLayout sidebar={sidebar}>{children}</DashboardLayout>;
}
